package manager.frame.user;

import manager.frame.LoginPanel;
import manager.frame.MainFrame;
import manager.util.SystemConstants;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

public class UserPanel extends JPanel {
    private static final JDesktopPane contentPane = new JDesktopPane();

    public UserPanel(){
        this.setBounds(0,0, SystemConstants.FRAME_WIDTH,SystemConstants.FRAME_HEIGHT);
        this.setLayout(new BorderLayout());

        //创建菜单栏
        JMenuBar menuBar = new JMenuBar();
        menuBar.setBounds(0,0,SystemConstants.FRAME_WIDTH,50);
        this.add(menuBar,BorderLayout.NORTH);

        this.add(contentPane,BorderLayout.CENTER);
        contentPane.removeAll();
        contentPane.repaint();

        //创建数据管理项菜单
        JMenu parentMenu = new JMenu("检查项管理");
        JMenu checkMenu = new JMenu("体检管理");
        JMenu systemMenu = new JMenu("系统管理");
        menuBar.add(parentMenu);
        menuBar.add(checkMenu);
        menuBar.add(systemMenu);

        //创建系统管理的二级菜单
        JMenuItem passwordMenu = new JMenuItem("修改密码");
        JMenuItem logoutMenu = new JMenuItem("退出登录");
        systemMenu.add(passwordMenu);
        systemMenu.add(logoutMenu);

        //创建体检管理的二级菜单
        JMenuItem orderCheckMenu = new JMenuItem("预约体检");
        JMenuItem analyseCheckMenu = new JMenuItem("体检结果分析");
        checkMenu.add(orderCheckMenu);
        checkMenu.add(analyseCheckMenu);

        //预约体检
        orderCheckMenu.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                UserPanel.setContent(new OrderCheckPanel());
            }
        });

        //监听鼠标点击登出事件
        logoutMenu.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                contentPane.removeAll();
                contentPane.repaint();
                LoginPanel.user = null;
                MainFrame.setContent(new LoginPanel());
            }
        });
    }

    public static void setContent(JInternalFrame internalFrame){
        internalFrame.setSize(SystemConstants.FRAME_WIDTH - 15,SystemConstants.FRAME_HEIGHT);
        internalFrame.setVisible(true);

        contentPane.removeAll();
        contentPane.repaint();
        contentPane.add(internalFrame);
    }
}
